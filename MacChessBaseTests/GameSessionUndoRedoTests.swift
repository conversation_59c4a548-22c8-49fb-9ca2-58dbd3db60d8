//
//  GameSessionUndoRedoTests.swift
//  MacChessBaseTests
//
//  Tests for GameSession undo/redo functionality
//

import XCTest
@testable import MacChessBase
@testable import ChessKit

@MainActor
final class GameSessionUndoRedoTests: XCTestCase {
    
    var gameSession: GameSession!
    var undoManager: UndoManager!
    
    override func setUp() {
        super.setUp()
        gameSession = GameSession()
        undoManager = UndoManager()
        gameSession.undoManager = undoManager
    }
    
    override func tearDown() {
        gameSession = nil
        undoManager = nil
        super.tearDown()
    }
    
    // MARK: - Initial State Tests
    
    func testInitialUndoRedoState() {
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
        XCTAssertEqual(gameSession.undoActionName, "")
        XCTAssertEqual(gameSession.redoActionName, "")
    }
    
    // MARK: - Move Operations Tests
    
    func testMakeMoveUndoRedo() {
        // Initial state verification
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertEqual(gameSession.currentMoveIndex, gameSession.game.startingIndex)
        
        // Make a move (e2-e4)
        let success = gameSession.makeMove(from: Square.e2, to: Square.e4)
        XCTAssertTrue(success)
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
        XCTAssertEqual(gameSession.undoActionName, "Move")
        
        // Verify move was made
        let moveIndex = gameSession.currentMoveIndex
        XCTAssertNotEqual(moveIndex, gameSession.game.startingIndex)
        let move = gameSession.game.moves.getNodeMove(index: moveIndex)
        XCTAssertNotNil(move)
        XCTAssertEqual(move?.metaMove?.start, .e2)
        XCTAssertEqual(move?.metaMove?.end, .e4)
        
        // Test undo
        undoManager.undo()
        
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.redoActionName, "Move")
        XCTAssertEqual(gameSession.currentMoveIndex, gameSession.game.startingIndex)
        
        // Test redo
        undoManager.redo()
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
    }
    
    func testMultipleMoveUndoRedo() async {
        // Make multiple moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        _ = gameSession.makeMove(from: Square.g1, to: Square.f3)
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
        
        // Undo all moves
        undoManager.undo()
        undoManager.undo()
        undoManager.undo()
        
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.currentMoveIndex, gameSession.game.startingIndex)
        
        // Redo all moves
        undoManager.redo()
        undoManager.redo()
        undoManager.redo()
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
    }
    
    // MARK: - Delete Operations Tests
    
    func testDeleteMoveUndoRedo() async {
        // Set up a game with moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let moveIndex = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        let secondMoveIndex = gameSession.currentMoveIndex
        
        // Delete the second move
        let deleteSuccess = gameSession.deleteMove(at: secondMoveIndex)
        XCTAssertTrue(deleteSuccess)
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertEqual(gameSession.undoActionName, "Delete Move")
        XCTAssertEqual(gameSession.currentMoveIndex, moveIndex)
        
        // Undo delete
        undoManager.undo()
                
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.redoActionName, "Delete Move")
        
        // Redo delete
        undoManager.redo()
    }
    
    func testDeleteBeforeMoveUndoRedo() async {
        // Set up a game with multiple moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        let targetIndex = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.g1, to: Square.f3)
        
        let _ = gameSession.game.startingIndex
        
        // Delete before the target move
        let success = gameSession.deleteBeforeMove(at: targetIndex)
        XCTAssertTrue(success)
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertEqual(gameSession.undoActionName, "Delete Before Move")
        XCTAssertEqual(gameSession.currentMoveIndex, targetIndex)
        
        // Undo delete before
        undoManager.undo()
        
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.redoActionName, "Delete Before Move")
        
        // Redo delete before
        undoManager.redo()
    }
    
    // MARK: - Variation Promotion Tests
    
    func testPromoteVariationUndoRedo() async {
        // Create a variation
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let baseIndex = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        _ = gameSession.currentMoveIndex
        
        // Go back and create variation
        gameSession.goToMove(at: baseIndex)
        _ = gameSession.makeMove(from: .d7, to: .d5) // Different move to create variation
        let variationIndex = gameSession.currentMoveIndex
        
        // Promote the variation
        let success = gameSession.promoteVariation(at: variationIndex)
        XCTAssertTrue(success)
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertEqual(gameSession.undoActionName, "Promote Variation")
        
        // Undo promotion
        undoManager.undo()
        
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.redoActionName, "Promote Variation")
        
        // Redo promotion
        undoManager.redo()
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
    }
    
    func testPromoteToMainVariationUndoRedo() async {
        // Create a variation
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let baseIndex = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        
        // Go back and create variation
        gameSession.goToMove(at: baseIndex)
        _ = gameSession.makeMove(from: .d7, to: .d5) // Different move to create variation
        let variationIndex = gameSession.currentMoveIndex
        
        // Promote to main variation
        let success = gameSession.promoteToMainVariation(at: variationIndex)
        XCTAssertTrue(success)
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertEqual(gameSession.undoActionName, "Promote to Main")
        
        // Undo promotion to main
        undoManager.undo()
        
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.redoActionName, "Promote to Main")
        
        // Redo promotion to main
        undoManager.redo()
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
    }
    
    // MARK: - Overwrite Operation Tests
    
    func testOverwriteMoveUndoRedo() async {
        // Set up moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let baseIndex = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        _ = gameSession.makeMove(from: Square.g1, to: Square.f3)
        
        let previousIndex = gameSession.currentMoveIndex
        
        // Go back to overwrite position
        gameSession.goToMove(at: baseIndex)
        
        // Create a move to overwrite with
        var tempBoard = gameSession.board
        guard let overwriteMove = tempBoard.move(pieceAt: .d7, to: .d5) else {
            XCTFail("Failed to create overwrite move")
            return
        }
        
        // Overwrite move
        let newIndex = gameSession.overwriteMove(overwriteMove, from: baseIndex)
        XCTAssertNotEqual(newIndex, previousIndex)
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertEqual(gameSession.undoActionName, "Overwrite Move")
        
        // Undo overwrite
        undoManager.undo()
        
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.redoActionName, "Overwrite Move")
        
        // Redo overwrite
        undoManager.redo()
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
    }
    
    // MARK: - Move Editing Tests
    
    func testEditMoveUndoRedo() async {
        // Make a move
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let moveIndex = gameSession.currentMoveIndex
        
        // Get the original move
        guard var move = gameSession.game.moves.getNodeMove(index: moveIndex) else {
            XCTFail("No move found at index")
            return
        }
        
        // Edit the move (add a comment)
        move.positionComment.text = "Good opening move"
        let editSuccess = gameSession.editMove(at: moveIndex, newMove: move)
        XCTAssertTrue(editSuccess)
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertEqual(gameSession.undoActionName, "Edit Move")
        
        // Verify edit was applied
        let editedMove = gameSession.game.moves.getNodeMove(index: moveIndex)
        XCTAssertEqual(editedMove?.positionComment.text, "Good opening move")
        
        // Undo edit
        undoManager.undo()
        
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.redoActionName, "Edit Move")
        
        // Verify undo worked
        let undoneMove = gameSession.game.moves.getNodeMove(index: moveIndex)
        XCTAssertEqual(undoneMove?.positionComment.text, nil)
        
        // Redo edit
        undoManager.redo()
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
        
        // Verify redo worked
        let _ = gameSession.game.moves.getNodeMove(index: moveIndex)
    }
    
    // MARK: - Complex Operation Tests
    
    func testMixedOperationsUndoRedo() async {
        // Perform a series of different operations
        
        // Make moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let firstMoveIndex = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        let _ = gameSession.currentMoveIndex
        
        // 2. Edit a move
        guard var move = gameSession.game.moves.getNodeMove(index: firstMoveIndex) else {
            XCTFail("Move not found")
            return
        }
        move.positionComment.text = "King's pawn"
        _ = gameSession.editMove(at: firstMoveIndex, newMove: move)
        
        // 3. Make another move
        _ = gameSession.makeMove(from: Square.g1, to: Square.f3)
        let thirdMoveIndex = gameSession.currentMoveIndex
        
        // 4. Delete a move
        _ = gameSession.deleteMove(at: thirdMoveIndex)
        
        // Verify we can undo all operations
        XCTAssertTrue(gameSession.canUndo)
        
        // Undo delete
        undoManager.undo()
        
        // Undo move
        undoManager.undo()
        
        // Undo edit
        undoManager.undo()
        let editedMove = gameSession.game.moves.getNodeMove(index: firstMoveIndex)
        XCTAssertEqual(editedMove?.positionComment.text, nil)
        
        // Undo moves
        undoManager.undo()
        undoManager.undo()
        
        XCTAssertEqual(gameSession.currentMoveIndex, gameSession.game.startingIndex)
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertTrue(gameSession.canRedo)
    }
    
    // MARK: - Edge Cases Tests
    
    func testUndoRedoWithoutOperations() {
        // Test undo/redo when there are no operations
        gameSession.undo()
        gameSession.redo()
        
        // Should remain in initial state
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
        XCTAssertEqual(gameSession.currentMoveIndex, gameSession.game.startingIndex)
    }
    
    func testUndoRedoStateConsistency() async {
        // Make a move
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let moveIndex = gameSession.currentMoveIndex
        
        // Verify state
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
        
        // Undo
        undoManager.undo()
        
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.currentMoveIndex, gameSession.game.startingIndex)
        
        // Make a different move (should clear redo stack)
        _ = gameSession.makeMove(from: .d2, to: .d4)
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo) // Redo stack should be cleared
        XCTAssertNotEqual(gameSession.currentMoveIndex, moveIndex)
    }
    
    // MARK: - Consecutive Operations Tests
    
    func testConsecutiveUndoRedoNoDelay() {
        // Make some moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let _ = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        let _ = gameSession.currentMoveIndex
        _ = gameSession.makeMove(from: Square.g1, to: Square.f3)
        let thirdMoveIndex = gameSession.currentMoveIndex
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
        XCTAssertEqual(gameSession.currentMoveIndex, thirdMoveIndex)
        
        // Consecutive undo operations without delay (like rapid user clicks)
        undoManager.undo() // Should undo Nf3
        undoManager.undo() // Should undo e5
        undoManager.undo() // Should undo e4
        
        XCTAssertFalse(gameSession.canUndo)
        XCTAssertTrue(gameSession.canRedo)
        XCTAssertEqual(gameSession.currentMoveIndex, gameSession.game.startingIndex)
        
        // Now consecutive redo operations without delay (this is where the issue occurs)
        undoManager.redo() // Should redo e4 -> this works
        print("After first redo: canRedo=\(gameSession.canRedo), currentIndex=\(gameSession.currentMoveIndex)")
        
        undoManager.redo() // Should redo e5 -> this might fail
        print("After second redo: canRedo=\(gameSession.canRedo), currentIndex=\(gameSession.currentMoveIndex)")
        
        undoManager.redo() // Should redo Nf3 -> this might fail  
        print("After third redo: canRedo=\(gameSession.canRedo), currentIndex=\(gameSession.currentMoveIndex)")
        
        // Check final state
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
    }
    
    func testRapidUndoRedoCycle() {
        // Make moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        let _ = gameSession.currentMoveIndex
        
        // Rapid cycle: undo -> undo -> redo -> redo (no delays)
        undoManager.undo()
        undoManager.undo()
        undoManager.redo() // First redo
        undoManager.redo() // Second redo - this is where the problem occurs
        
        XCTAssertTrue(gameSession.canUndo)
        XCTAssertFalse(gameSession.canRedo)
    }
    
    // MARK: - Import Operations Tests

    func testLoadGameFromObjectWithUndoRedo() async {
        // Set up initial game with some moves
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        _ = gameSession.makeMove(from: Square.e7, to: Square.e5)
        let originalPGN = gameSession.pgn
        let originalMoveIndex = gameSession.currentMoveIndex

        // Create a different game to import using PGN
        let newGamePGN = """
        [Event "Test"]
        [White "Player1"]
        [Black "Player2"]
        [Result "*"]

        1. d4 d5 *
        """
        guard let newGame = Game(pgn: newGamePGN) else {
            XCTFail("Failed to create game from PGN")
            return
        }

        // Import the new game with undo support (check if PGNs are different)
        let undoState = gameSession.loadGameFromObjectWithUndo(newGame)
        if originalPGN != newGamePGN {
            XCTAssertNotNil(undoState, "Undo state should be created for different PGN")
            XCTAssertNotEqual(gameSession.pgn, originalPGN)

            // Undo the import
            if let undoState {
                let undoSuccess = gameSession.undoImportGame(undoState)
                XCTAssertTrue(undoSuccess)
                XCTAssertEqual(gameSession.pgn, originalPGN)
                XCTAssertEqual(gameSession.currentMoveIndex, originalMoveIndex)
            }
        } else {
            XCTAssertNil(undoState, "Undo state should not be created for identical PGN")
        }
    }

    func testLoadGameFromObjectWithUndoRedoSamePGN() {
        // Set up initial game
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let originalPGN = gameSession.pgn

        // Create identical game
        let identicalGame = Game(pgn: originalPGN)!

        // Import identical game - should not create undo state
        let undoState = gameSession.loadGameFromObjectWithUndo(identicalGame)
        XCTAssertNil(undoState, "Undo state should not be created for identical PGN")
        XCTAssertEqual(gameSession.pgn, originalPGN)
    }

    func testLoadPositionWithUndoRedo() async {
        // Set up initial game
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let originalPGN = gameSession.pgn
        let originalMoveIndex = gameSession.currentMoveIndex

        // Load a different position
        let newFEN = "rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq d3 0 1"
        let undoState = gameSession.loadPositionWithUndo(from: newFEN)
        XCTAssertNotNil(undoState, "Undo state should be created for different position")
        XCTAssertNotEqual(gameSession.pgn, originalPGN)

        // Undo the position load
        let undoSuccess = gameSession.undoImportGame(undoState!)
        XCTAssertTrue(undoSuccess)
        XCTAssertEqual(gameSession.pgn, originalPGN)
        XCTAssertEqual(gameSession.currentMoveIndex, originalMoveIndex)
    }

    func testLoadPositionWithUndoRedoInvalidFEN() {
        // Try to load invalid FEN
        let invalidFEN = "invalid fen string"
        let undoState = gameSession.loadPositionWithUndo(from: invalidFEN)
        XCTAssertNil(undoState, "Undo state should not be created for invalid FEN")
    }

    func testSetPositionUndoRedo() async {
        let viewModel = ChessGameViewModel(session: gameSession)

        // Set up initial game
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let originalPGN = gameSession.pgn
        let originalMoveIndex = gameSession.currentMoveIndex

        // Set up undo manager for the session
        let undoManager = UndoManager()
        gameSession.undoManager = undoManager

        // Create a different position
        let newPosition = Position(fen: "rnbqkbnr/pppppppp/8/8/3P4/8/PPP1PPPP/RNBQKBNR b KQkq d3 0 1")!

        // Set the position through viewModel
        viewModel.setPosition(newPosition)
        XCTAssertNotEqual(gameSession.pgn, originalPGN)
        XCTAssertTrue(undoManager.canUndo)
        XCTAssertEqual(undoManager.undoActionName, "Set Position")

        // Undo the position set
        undoManager.undo()

        XCTAssertEqual(gameSession.pgn, originalPGN)
        XCTAssertEqual(gameSession.currentMoveIndex, originalMoveIndex)
        XCTAssertTrue(undoManager.canRedo)
        XCTAssertEqual(undoManager.redoActionName, "Set Position")

        // Redo the position set
        undoManager.redo()

        XCTAssertNotEqual(gameSession.pgn, originalPGN)
        XCTAssertTrue(undoManager.canUndo)
        XCTAssertFalse(undoManager.canRedo)
    }

    func testLoadGameFromClipboardUndoRedo() async {
        let viewModel = ChessGameViewModel(session: gameSession)

        // Set up initial game
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let originalPGN = gameSession.pgn
        let originalMoveIndex = gameSession.currentMoveIndex

        // Set up undo manager for the session
        let undoManager = UndoManager()
        gameSession.undoManager = undoManager

        // Set up clipboard with different PGN
        let clipboardPGN = """
        [Event "Test"]
        [White "Player1"]
        [Black "Player2"]
        [Result "*"]

        1. d4 d5 *
        """
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(clipboardPGN, forType: .string)

        // Load from clipboard
        viewModel.loadGameFromClipboard()
        XCTAssertNotEqual(gameSession.pgn, originalPGN)
        XCTAssertTrue(undoManager.canUndo)
        XCTAssertEqual(undoManager.undoActionName, "Import from Clipboard")

        // Undo the clipboard import
        undoManager.undo()

        XCTAssertEqual(gameSession.pgn, originalPGN)
        XCTAssertEqual(gameSession.currentMoveIndex, originalMoveIndex)
        XCTAssertTrue(undoManager.canRedo)
        XCTAssertEqual(undoManager.redoActionName, "Import from Clipboard")

        // Redo the clipboard import
        undoManager.redo()
        
        XCTAssertNotEqual(gameSession.pgn, originalPGN)
        XCTAssertTrue(undoManager.canUndo)
        XCTAssertFalse(undoManager.canRedo)
    }

    // MARK: - UI Integration Tests

    func testViewModelSynchronization() async {
        let viewModel = ChessGameViewModel(session: gameSession)

        // Make a move
        _ = gameSession.makeMove(from: Square.e2, to: Square.e4)
        let moveIndex = gameSession.currentMoveIndex

        // Verify viewModel is synchronized
        XCTAssertEqual(viewModel.session.currentMoveIndex, moveIndex)

        // Undo
        undoManager.undo()

        // Verify viewModel is still synchronized
        XCTAssertEqual(viewModel.session.currentMoveIndex, gameSession.game.startingIndex)
        XCTAssertEqual(viewModel.session.currentMoveIndex, gameSession.currentMoveIndex)
    }
}
